// Supabase configuration
// TODO: Replace these with your actual Supabase credentials
const SUPABASE_URL = 'https://ngdfziocjzjselkttgds.supabase.co'; // e.g., 'https://your-project.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5nZGZ6aW9janpqc2Vsa3R0Z2RzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NDYzNzAsImV4cCI6MjA2NTMyMjM3MH0.1zzRQGqpVY8TM_5NItjhNOTUJXDs-yoNe2QZnXlINsc'; // Your public anon key

// Initialize Supabase client
let supabase;

try {
    supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    console.log('Supabase client initialized successfully');
} catch (error) {
    console.error('Failed to initialize Supabase client:', error);
}

// Database configuration
const TABLE_NAME = 'clipboards'; // Adjust if your table name is different

// Expected table structure:
// CREATE TABLE clipboards (
//   id SERIAL PRIMARY KEY,
//   token VARCHAR(50) NOT NULL,
//   content TEXT,
//   updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
// );

// Database operations
const db = {
    // Get clipboard content for a token
    async getClipboard(token) {
        try {
            const { data, error } = await supabase
                .from(TABLE_NAME)
                .select('content, updated_at')
                .eq('token', token)
                .single();

            if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
                throw error;
            }

            return data;
        } catch (error) {
            console.error('Error getting clipboard:', error);
            throw error;
        }
    },

    // Update or insert clipboard content for a token
    async setClipboard(token, content) {
        try {
            const { data, error } = await supabase
                .from(TABLE_NAME)
                .upsert(
                    { 
                        token: token, 
                        content: content,
                        updated_at: new Date().toISOString()
                    },
                    { 
                        onConflict: 'token',
                        returning: 'minimal'
                    }
                );

            if (error) {
                throw error;
            }

            return data;
        } catch (error) {
            console.error('Error setting clipboard:', error);
            throw error;
        }
    },

    // Subscribe to real-time changes for a token
    subscribeToChanges(token, callback) {
        try {
            const channel = supabase
                .channel(`room:${token}`)
                .on(
                    'postgres_changes',
                    {
                        event: '*',
                        schema: 'public',
                        table: TABLE_NAME,
                        filter: `token=eq.${token}`
                    },
                    (payload) => {
                        console.log('Real-time update received:', payload);
                        callback(payload);
                    }
                )
                .subscribe((status) => {
                    console.log('Subscription status:', status);
                });

            return channel;
        } catch (error) {
            console.error('Error subscribing to changes:', error);
            throw error;
        }
    },

    // Unsubscribe from real-time changes
    unsubscribe(channel) {
        try {
            if (channel) {
                supabase.removeChannel(channel);
                console.log('Unsubscribed from real-time updates');
            }
        } catch (error) {
            console.error('Error unsubscribing:', error);
        }
    }
};

// Utility functions
const utils = {
    // Validate token format
    isValidToken(token) {
        return token && 
               typeof token === 'string' && 
               token.length >= 3 && 
               token.length <= 50 && 
               /^[a-zA-Z0-9_-]+$/.test(token);
    },

    // Generate a random token
    generateToken() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < 12; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },

    // Check if Supabase is properly configured
    isConfigured() {
        return SUPABASE_URL !== 'YOUR_SUPABASE_URL' && 
               SUPABASE_ANON_KEY !== 'YOUR_SUPABASE_ANON_KEY' &&
               supabase;
    }
};

// Export for use in other scripts
window.clipitDB = db;
window.clipitUtils = utils;
