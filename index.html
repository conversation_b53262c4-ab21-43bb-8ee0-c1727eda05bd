<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClipIt - Clipboard Sync</title>
    <link rel="stylesheet" href="style.css">
    <!-- Supabase CDN -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🔗 ClipIt</h1>
            <p>Sync your clipboard across devices</p>
        </header>

        <main>
            <div class="sync-section">
                <div class="input-group">
                    <label for="syncToken">Sync Token:</label>
                    <input 
                        type="text" 
                        id="syncToken" 
                        placeholder="Enter your sync token"
                        maxlength="50"
                    >
                    <button id="startSyncBtn" class="btn btn-primary">Start Sync</button>
                </div>
                
                <div class="status-indicator">
                    <span id="statusText">Not connected</span>
                    <div id="statusDot" class="status-dot offline"></div>
                </div>
            </div>

            <div class="clipboard-section">
                <div class="clipboard-header">
                    <h3>Clipboard Content</h3>
                    <div class="clipboard-actions">
                        <button id="readClipboardBtn" class="btn btn-secondary">Read Clipboard</button>
                        <button id="clearBtn" class="btn btn-secondary">Clear</button>
                    </div>
                </div>
                
                <textarea 
                    id="clipboardContent" 
                    placeholder="Your clipboard content will appear here..."
                    rows="10"
                ></textarea>
                
                <div class="clipboard-actions">
                    <button id="writeClipboardBtn" class="btn btn-primary">Write to Clipboard</button>
                    <button id="copyBtn" class="btn btn-secondary">Copy Text</button>
                </div>
            </div>

            <div class="logs-section">
                <h3>Activity Log</h3>
                <div id="logContainer" class="log-container">
                    <p class="log-entry">Welcome to ClipIt! Enter a sync token to get started.</p>
                </div>
                <button id="clearLogsBtn" class="btn btn-secondary">Clear Logs</button>
            </div>
        </main>

        <footer>
            <p>Built with ❤️ using Supabase</p>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="supabase.js"></script>
    <script src="script.js"></script>
</body>
</html>
